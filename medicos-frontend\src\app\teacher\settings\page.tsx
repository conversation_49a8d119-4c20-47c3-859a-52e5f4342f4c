"use client"

import { SettingsForm } from "@/components/teacher/settings-form"

export default function SettingsPage() {
  return (
    <div className="container py-10">
      <SettingsForm
        defaultValues={{
          firstName: "<PERSON><PERSON>",
          phoneNumber: "+91 99884-56252",
          email: "<EMAIL>",
          username: "<PERSON><PERSON><PERSON>",
          bio: "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
        }}
        onSubmit={(data) => {
          // In a real app, you would save the data to your backend
          console.log("Form submitted:", data)
          alert("Settings saved successfully!")
        }}
        onCancel={() => {
          // Handle cancel action
          console.log("Form cancelled")
          alert("Changes discarded")
        }}
      />
    </div>
  )
}
