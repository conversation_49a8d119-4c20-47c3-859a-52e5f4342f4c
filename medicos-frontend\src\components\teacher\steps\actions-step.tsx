"use client"

import type { FormData } from "../question-paper-wizard"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, Eye, Edit } from "lucide-react"

type ActionsStepProps = {
  formData: FormData
  onSubmit: () => void
}

export function ActionsStep({ formData, onSubmit }: ActionsStepProps) {
  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Actions</h2>
        <p className="text-gray-500">Finalize your selections.</p>
      </div>

      <div className="flex flex-col items-center gap-4 py-4">
        <Button variant="outline" className="w-full max-w-xs flex gap-2">
          <Edit className="h-4 w-4" />
          Edit Questions
        </Button>

        <Button className="w-full max-w-xs flex gap-2 bg-[#2563EB] hover:bg-[#2563EB]/90" onClick={onSubmit}>
          <Download className="h-4 w-4" />
          Download PDF
        </Button>

        <Button variant="ghost" className="flex gap-2">
          <Eye className="h-4 w-4" />
          Preview
        </Button>
      </div>
    </div>
  )
}
