"use client"

import type { FormData } from "../question-paper-wizard"
import { OptionButton } from "../ui/option-button"
import { StepNavigation } from "../ui/step-navigation"

type QuestionTypeStepProps = {
  formData: FormData
  updateFormData: (data: Partial<FormData>) => void
  onNext: () => void
  onSkip: () => void
  onBack: () => void
  backDisabled: boolean
}

export function QuestionTypeStep({ formData, updateFormData, onNext, onSkip, onBack, backDisabled }: QuestionTypeStepProps) {
  const handleSelect = (type: string) => {
    updateFormData({ questionType: type })
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-semibold">Question Type</h2>
        <p className="text-gray-500">Select the types of questions you want.</p>
      </div>

      <div className="flex justify-center py-4">
        <div className="inline-flex rounded-sm border border-gray-200 overflow-hidden min-h-[48px]">
          <OptionButton
            selected={formData.questionType === "NEET"}
            onClick={() => handleSelect("NEET")}
            grouped={true}
            position="left"
            className="rounded-none border-0"
          >
            NEET
          </OptionButton>
          <div className="w-px bg-gray-200 min-h-full"></div>
          <OptionButton
            selected={formData.questionType === "CEET"}
            onClick={() => handleSelect("CEET")}
            grouped={true}
            position="right"
            className="rounded-none border-0"
          >
            CEET
          </OptionButton>
        </div>
      </div>

      <StepNavigation 
        onNext={onNext} 
        onSkip={onSkip} 
        onBack={onBack}
        backDisabled={backDisabled}
        nextDisabled={!formData.questionType} 
      />
    </div>
  )
}
