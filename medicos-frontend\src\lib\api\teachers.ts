import { api } from './api';

export interface TeacherData {
  name: string;
  email: string;
  phone: string;
  department?: string;
  designation?: string;
}

interface AddTeacherData {
  name: string;
  email: string;
  phone: string;
  department?: string;
  designation?: string;
}

export const addTeacherToCollege = async (collegeId: string, data: AddTeacherData) => {
  try {
    // Get the token from localStorage
    const token = localStorage.getItem('token') || localStorage.getItem('backendToken');
    
    if (!token) {
      throw new Error('Authentication token is missing. Please log in again.');
    }
    
    // Set the authorization header with the token
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
    
    // Make the API request with the token
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || ''}/colleges/${collegeId}/teachers`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    });
    
    // Handle non-2xx responses
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // If unauthorized (401) or forbidden (403), don't automatically log out
      if (response.status === 401 || response.status === 403) {
        throw new Error(errorData.message || 'You are not authorized to perform this action.');
      }
      
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error: any) {
    // Don't throw errors that would cause a redirect to login
    console.error('Error adding teacher:', error);
    throw new Error(error.message || 'Failed to add teacher. Please try again.');
  }
};

// Get teachers for a college with pagination
export const getCollegeTeachers = async (
  collegeId: string, 
  page = 1, 
  limit = 10,
  filters = {}
) => {
  try {
    const token = localStorage.getItem('token') || localStorage.getItem('backendToken');
    
    if (!token) {
      console.error('No authentication token found');
      throw new Error('Authentication token is missing. Please log in again.');
    }
    
    // Build query parameters
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...filters
    });
    
    // Update the URL to match the backend route structure
    const url = `${process.env.NEXT_PUBLIC_API_URL || ''}/colleges/${collegeId}/teachers?${queryParams}`;
    console.log(`Fetching teachers: ${url}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      cache: 'no-store' // Prevent caching
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      try {
        const errorData = JSON.parse(errorText);
        throw new Error(errorData.message || `Error: ${response.status}`);
      } catch (e) {
        throw new Error(`Error: ${response.status} - ${response.statusText}`);
      }
    }
    
    const data = await response.json();
    console.log("Raw API response:", data);
    
    // If the API returns an array directly, wrap it in an object with pagination info
    if (Array.isArray(data)) {
      console.log("API returned an array, converting to paginated format");
      return {
        teachers: data,
        total: data.length,
        page: page,
        limit: limit,
        totalPages: Math.ceil(data.length / limit)
      };
    }
    
    return data;
  } catch (error: any) {
    console.error('Error fetching college teachers:', error);
    throw error;
  }
};

// Update a teacher
export const updateTeacher = async (teacherId: string, teacherData: any) => {
  try {
    const token = localStorage.getItem('token') || localStorage.getItem('backendToken');
    
    if (!token) {
      throw new Error('Authentication token is missing. Please log in again.');
    }
    
    console.log('Updating teacher with data:', teacherData);
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || ''}/teachers/${teacherId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify(teacherData)
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error: any) {
    console.error('Error updating teacher:', error);
    throw error;
  }
};

// Delete a teacher
export const deleteTeacher = async (teacherId: string) => {
  try {
    const token = localStorage.getItem('token') || localStorage.getItem('backendToken');
    
    if (!token) {
      throw new Error('Authentication token is missing. Please log in again.');
    }
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
    
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || ''}/teachers/${teacherId}`, {
      method: 'DELETE',
      headers
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Error: ${response.status}`);
    }
    
    return await response.json();
  } catch (error: any) {
    console.error('Error deleting teacher:', error);
    throw new Error(error.message || 'Failed to delete teacher');
  }
};














