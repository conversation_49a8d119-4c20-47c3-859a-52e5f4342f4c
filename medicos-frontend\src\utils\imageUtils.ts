/**
 * Utility functions for handling images, including base64 detection and conversion
 */

/**
 * Checks if a string is a base64 encoded image
 */
export function isBase64Image(str: string): boolean {
  if (!str || typeof str !== 'string') return false;
  
  // Check for data URL format
  const dataUrlPattern = /^data:image\/(png|jpg|jpeg|gif|webp|svg\+xml);base64,/i;
  if (dataUrlPattern.test(str)) return true;
  
  // Check for raw base64 (without data URL prefix)
  // Base64 strings are typically long and contain only valid base64 characters
  const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;
  return str.length > 100 && base64Pattern.test(str);
}

/**
 * Converts a base64 string to a data URL if it's not already one
 */
export function ensureDataUrl(base64String: string): string {
  if (!base64String) return '';
  
  // If it's already a data URL, return as is
  if (base64String.startsWith('data:image/')) {
    return base64String;
  }
  
  // If it's raw base64, add the data URL prefix
  // Default to PNG if we can't determine the format
  return `data:image/png;base64,${base64String}`;
}

/**
 * Extracts and processes images from text content
 * Returns an object with cleaned text and extracted images
 */
export function extractImagesFromText(text: string): {
  cleanText: string;
  images: Array<{ id: string; src: string; alt: string }>;
} {
  if (!text) return { cleanText: text, images: [] };
  
  const images: Array<{ id: string; src: string; alt: string }> = [];
  let cleanText = text;
  
  // Look for base64 patterns in the text
  const base64Patterns = [
    // Data URL pattern
    /data:image\/[^;]+;base64,[A-Za-z0-9+/]+=*/g,
    // Raw base64 pattern (more conservative - must be quite long)
    /\b[A-Za-z0-9+/]{200,}={0,2}\b/g
  ];
  
  base64Patterns.forEach((pattern, patternIndex) => {
    const matches = text.match(pattern);
    if (matches) {
      matches.forEach((match, matchIndex) => {
        if (isBase64Image(match)) {
          const imageId = `extracted-image-${patternIndex}-${matchIndex}`;
          const imageSrc = ensureDataUrl(match);
          
          images.push({
            id: imageId,
            src: imageSrc,
            alt: `Extracted image ${images.length + 1}`
          });
          
          // Remove the base64 string from the text
          cleanText = cleanText.replace(match, `[Image ${images.length}]`);
        }
      });
    }
  });
  
  return { cleanText, images };
}

/**
 * Component props for rendering base64 images safely
 */
export interface Base64ImageProps {
  src: string;
  alt?: string;
  className?: string;
  maxWidth?: number;
  maxHeight?: number;
}

/**
 * Validates and sanitizes base64 image source
 */
export function validateBase64ImageSrc(src: string): string | null {
  if (!src || !isBase64Image(src)) return null;
  
  try {
    const dataUrl = ensureDataUrl(src);
    // Additional validation could be added here
    return dataUrl;
  } catch (error) {
    console.warn('Invalid base64 image source:', error);
    return null;
  }
}
