// auth/auth.service.ts
import {
  Injectable,
  UnauthorizedException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as bcrypt from 'bcrypt';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { User, UserDocument } from '../schema/user.schema';
import { FirebaseAuthService } from './firebase-auth.service';
import { AuthResponseDto } from './dto/auth-response.dto';
import { College } from '../schema/college.schema';

@Injectable()
export class AuthService {
  constructor(
    @InjectModel(User.name) private userModel: Model<UserDocument>,
    @InjectModel(College.name) private collegeModel: Model<College>,
    private jwtService: JwtService,
    private firebaseAuthService: FirebaseAuthService,
  ) {}

  async validateUser(email: string, password: string): Promise<UserDocument> {
    const user = await this.userModel.findOne({ email });
    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // For users registered with username/password
    if (user.password) {
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        throw new UnauthorizedException('Invalid credentials');
      }
      return user;
    }

    throw new UnauthorizedException('Invalid authentication method');
  }

  async login(loginDto: LoginDto): Promise<AuthResponseDto> {
    try {
      let user: UserDocument | null;

      // Check login method
      if (loginDto.firebaseToken) {
        try {
          // Firebase token authentication
          const decodedToken = await this.firebaseAuthService.verifyToken(
            loginDto.firebaseToken,
          );

          // Ensure UID is a string
          const firebaseUid = String(decodedToken.uid);

          // Find user by Firebase UID
          user = await this.userModel.findOne({ firebaseUid });

          // If user doesn't exist but is authenticated with Firebase, create the user
          if (!user) {
            const firebaseUser =
              await this.firebaseAuthService.getUserByUid(firebaseUid);

            // Check if user with this email already exists
            const existingUser = await this.userModel.findOne({
              email: firebaseUser.email,
            });
            if (existingUser) {
              // Link Firebase UID to existing user
              existingUser.firebaseUid = firebaseUid;
              user = await existingUser.save();
            } else {
              // Check if the email is registered in any college's teachers or admins array
              const college = await this.collegeModel
                .findOne({
                  $or: [
                    { teachers: { $in: [firebaseUser.email] } },
                    { admins: { $in: [firebaseUser.email] } }
                  ]
                })
                .exec();

              if (!college) {
                throw new UnauthorizedException(
                  'Email is not authorized. Please contact your college administrator.',
                );
              }

              // Determine user role based on which array contains the email
              let userRole = 'teacher'; // Default role
              if (college.admins.includes(firebaseUser.email || '')) {
                userRole = 'collegeAdmin';
              }

              // Create new user
              user = await this.userModel.create({
                email: firebaseUser.email,
                displayName: firebaseUser.displayName || firebaseUser.email,
                firebaseUid,
                role: userRole,
                status: 'active',
                collegeId: college._id,
              });
            }
          }
        } catch (error) {
          throw new UnauthorizedException(
            `Firebase authentication failed: ${error.message}`,
          );
        }
      } else if (loginDto.email && loginDto.password) {
        // Traditional username/password authentication
        user = await this.validateUser(loginDto.email, loginDto.password);
      } else {
        throw new UnauthorizedException(
          'Invalid login credentials: must provide either Firebase token or email/password',
        );
      }

      // Update last login
      user.lastLogin = new Date();
      await user.save();

      // Generate JWT token
      const payload: any = {
        sub: user._id,
        email: user.email,
        role: user.role,
      };

      // Only include collegeId for non-superAdmin users and only if they have an associated college
      if (user.role !== 'superAdmin' && user.collegeId) {
        // Ensure collegeId is a string
        payload.collegeId =
          typeof user.collegeId === 'string'
            ? user.collegeId
            : user.collegeId.toString();
      }

      // Safely convert MongoDB ObjectId to string
      const userId = user._id
        ? typeof user._id === 'string'
          ? user._id
          : user._id.toString()
        : user.id
          ? typeof user.id === 'string'
            ? user.id
            : user.id.toString()
          : null;

      if (!userId) {
        throw new UnauthorizedException('User ID is missing or invalid');
      }

      // Safely convert collegeId to string if it exists
      let collegeIdStr: string | undefined = undefined;
      if (user.collegeId) {
        if (typeof user.collegeId === 'string') {
          collegeIdStr = user.collegeId;
        } else {
          // Handle MongoDB document with toString() method
          collegeIdStr = user.collegeId.toString();
        }
      }

      return {
        accessToken: this.jwtService.sign(payload),
        user: {
          id: userId,
          email: user.email,
          displayName: user.displayName,
          role: user.role,
          collegeId: collegeIdStr,
        },
      };
    } catch (error) {
      throw new UnauthorizedException(
        'Authentication failed: ' + error.message,
      );
    }
  }

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    // Step 1: Check if email exists in any college's teachers or admins array
    const college = await this.collegeModel
      .findOne({
        $or: [
          { teachers: { $in: [registerDto.email] } },
          { admins: { $in: [registerDto.email] } }
        ]
      })
      .exec();

    if (!college) {
      throw new BadRequestException(
        'Email is not authorized. Please contact your college administrator.',
      );
    }

    // Step 2: Check if user with this email already exists in the User collection
    const existingUser = await this.userModel.findOne({
      email: registerDto.email,
    });
    if (existingUser) {
      throw new ConflictException('User is already registered');
    }

    // Step 3: Determine user role based on which array contains the email
    let userRole = registerDto.role; // Default to provided role

    if (college.admins.includes(registerDto.email)) {
      userRole = 'collegeAdmin'; // Override role to collegeAdmin if email is in admins array
    } else if (college.teachers.includes(registerDto.email)) {
      userRole = 'teacher'; // Override role to teacher if email is in teachers array
    }

    // Step 4: Create new user if college email matched and user doesn't exist
    const hashedPassword = await bcrypt.hash(registerDto.password, 10);

    // Explicitly destructure to ensure collegeId from registerDto is not used if present
    const { collegeId, ...restOfRegisterDto } = registerDto;

    const newUser = await this.userModel.create({
      ...restOfRegisterDto, // Uses email, displayName, firstName, lastName from DTO
      role: userRole, // Use determined role instead of DTO role
      password: hashedPassword,
      collegeId: college._id, // Assign the ID of the matched college
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLogin: new Date(), // Initialize lastLogin, or set to null if preferred
    });

    // Step 4: Generate JWT token
    const payload: any = {
      sub: newUser._id,
      email: newUser.email,
      role: newUser.role,
    };

    // Only include collegeId for non-superAdmin users and only if they have an associated college
    if (newUser.role !== 'superAdmin' && newUser.collegeId) {
      // Ensure collegeId is a string
      payload.collegeId =
        typeof newUser.collegeId === 'string'
          ? newUser.collegeId
          : newUser.collegeId.toString();
    }

    return {
      accessToken: this.jwtService.sign(payload),
      user: {
        id: newUser._id ? newUser._id.toString() : newUser.id?.toString(),
        email: newUser.email,
        displayName: newUser.displayName,
        role: newUser.role,
        collegeId: newUser.collegeId?.toString(),
      },
    };
  }
}
